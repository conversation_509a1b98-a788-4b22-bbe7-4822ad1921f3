"use client";
import FreeSlotsSidebar from "@/components/dashboard/FreeSlotsSidebar";
import ScheduleSessionSidebar from "@/components/dashboard/ScheduleSessionSidebar";
import DashboardLayout from "@/layout/dashboard/DashboardLayout";
import { CaretDown, Check, MagnifyingGlass } from "@phosphor-icons/react";
import Image from "next/image";
import React, { useEffect, useRef, useState, useCallback, useMemo } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Swiper as SwiperType } from "swiper/types"; //
import {
  GreenUsersIcon,
  OrangeCalendarIcon,
  PendingPayIcon,
  ReceivedPayIcon,
} from "../../../public/assets/Svgs";

import Button from "@/components/common/Button";
import Modal from "@/components/common/Modal";
import TablePagination from "@/components/common/TablePagination";
import Tabs from "@/components/common/Tabs";
import AnalyticsCard from "@/components/dashboard/analytics/AnalyticsCard";
import BusinessDataOverview from "@/components/dashboard/analytics/BusinessDataOverview";
import PaymentOverviewChart from "@/components/dashboard/analytics/PaymentOverviewChart";
import ClientInsightsAccordion from "@/components/dashboard/analytics/ClientInsightsAccordion";
import CommonModal from "@/components/dashboard/CommonModal";
import RescheduleSidebar from "@/components/dashboard/RescheduleSidebar";
import DashboardTBody, {
  Item,
} from "@/components/dashboard/common/table/DashboardTBody";
import THeader from "@/components/dashboard/common/table/THeader";
import {
  deleteSchedules,
  scheduleReminder,
  useGetSchedules,
} from "@/services/session.service";
import { useGetSettingData } from "@/services/setting.service";
import { formatDate } from "@/utils/axios";
import moment from "moment";
import { useRouter } from "next/navigation";
import "swiper/css";
import "swiper/css/navigation";
import { Navigation } from "swiper/modules";
import "swiper/swiper-bundle.css";
import { mutate } from "swr";
import {
  useCheckSyncCalendarStatus,
  useGetCancellation,
  useGetDashboardStats,
  useGetMostRecentClientRequest,
} from "../../services/dashboard.service";
import { SessionData } from "../session/page";
import Link from "next/link";
import {
  useGetTotalWorkingHours,
  useGetStrongRelationships,
  useGetPeopleHelping,
  useGetRuptureRepair,
  useGetBusiestTimeSlots,
  useGetBusiestDays,
  useGetHighestCancellationDay,
  useGetPaymentSummary,
  type BusiestTimeSlotsData,
  type BusiestDaysData,
  type HighestCancellationDayData,
  type PaymentSummaryData
} from '@/services/analytics.service';
import BusinessDateFilter from '@/components/common/BusinessDateFilter';
import AnalyticsDateFilter from '@/components/common/AnalyticsDateFilter';
import { startOfWeek, endOfWeek } from 'date-fns';

const sessionTableHeader = [
  "Name",
  "Time",
  "status",
  "Amount",
  "this session Fee	",
  "Previous Fee	",
  "Appointment",
  "actions",
];

interface MostRecentClientItem {
  name: string;
  email: string;
  last_session: {
    date: string;
    minutes: number;
  };
  session: {
    total_session: number;
    completed_session: number;
    pending_session: number;
    total: number;
    completed_total: number;
    pending_total: number;
  };
}

const sessionTabs = [
  { label: "Upcoming ", value: "upcoming" },
  { label: "Public Calendar ", value: "public-calendar" },
  { label: "Completed ", value: "completed" },
  { label: "Cancelled ", value: "cancelled" },
  { label: "All ", value: "" },
];

const dateFormatter = (year: string, month: string, day: number): string => {
  const monthIndex = new Date(`${month} 1, ${year}`).getMonth(); // Get the month index (0-based)

  // Create a Date object and set the time to noon to avoid timezone issues
  const date = new Date(Number(year), monthIndex, day, 12, 0, 0); // Set hours to 12:00 PM

  const formattedDate = date.toISOString(); // Convert to ISO format
  return formattedDate;
};

const Dashboard = () => {
  const router = useRouter();
  const [activeTable, setActiveTable] = useState(sessionTabs[0]);
  const [freeSlot, setFreeSlot] = useState(false);
  const [isScheduleSessionModal, setIsScheduleSessionModal] = useState(false);
  const [isRescheduleSession, setIsRescheduleSession] = useState(false);
  // const [isReminderModal, setIsReminderModal] = useState(false);
  const [isReminderMassageModal, setIsReminderMassageModal] = useState(false);
  const [isCanceledSessionModal, setIsCanceledSessionModal] = useState(false);
  const [isCancellationModal, setIsCancellationModal] = useState(false);
  const [isUpdatePaymentModal, setIsUpdatePaymentModal] = useState(false);
  const [isTerminatingModal, setIsTerminatingModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [singleSessionID, setSingleSessionID] = useState("");
  const [isOpenSetupCalender, setIsOpenSyncCalender] = useState(false);
  const [loading, setLoading] = useState(false); // Loader state

  // Activities section date state - should be updated based on filter selection
  const [activitiesStartDate, setActivitiesStartDate] = useState(() => {
    const start = new Date();
    start.setDate(1); // First day of current month
    start.setHours(0, 0, 0, 0); // Set to start of the day (00:00:00)
    return start;
  });

  const [activitiesEndDate, setActivitiesEndDate] = useState(() => {
    const end = new Date();
    // Last day of current month
    end.setMonth(end.getMonth() + 1, 0);
    end.setHours(23, 59, 59, 999); // Set to end of the day (23:59:59)
    return end;
  });

  const [cancelledStartDate] = useState(() => {
    const start = new Date();
    start.setHours(0, 0, 0, 0); // Set to start of the day (00:00:00)
    return start;
  });

  const [cancelledEndDate] = useState(() => {
    const end = new Date();
    end.setHours(23, 59, 59, 999); // Set to end of the day (23:59:59)
    return end;
  });

  const [singleSessionData, setSingleSessionData] =
    useState<SessionData | null>(null);

  const accordionSections = [
    {
      id: "power_clients",
      title: "Strong Relationships With",
      color: "#FBE9D0",
      isExpanded: false,
      clients: [],
    },
    {
      id: "risk_clients",
      title: "Rupture & Repair Needed With",
      color: "#FBD7D8",
      isExpanded: false,
      clients: [
        {
          id: "1",
          name: "Liam Carter",
          email: "<EMAIL>",
          avatar: "LC",
          count: 5,
          type: "delays" as const,
        },
        {
          id: "2",
          name: "Ayush Dobariya",
          email: "<EMAIL>",
          avatar: "AD",
          count: 5,
          type: "cancellation" as const,
        },
        {
          id: "3",
          name: "Pankaj Patil",
          email: "<EMAIL>",
          avatar: "PP",
          count: 5,
          type: "reschedules" as const,
        },
      ],
    },
    {
      id: "see_more_insights",
      title: "See More Insights",
      color: "#D4F4DD",
      isExpanded: false,
      clients: [],
    },
  ];

  // Filter params for public calendar
  const [filterparams, setFilterparams] = useState<{
    fromPublicCalender?: boolean;
  }>({});

  // select drop down stats
  // Removed unused dropdown state variables

  function handleModalTransition(
    closeModal: (value: boolean) => void,
    openModal: (value: boolean) => void
  ) {
    closeModal(false); // Close the currently open modal
    openModal(true); // Open the next modal
  }

  const currentMonth = new Date().toLocaleString("default", { month: "short" }); // e.g., "Oct"
  const currentYear = new Date().getFullYear().toString(); // e.g., "2024"
  const today = new Date().getDate().toString().padStart(2, "0");

  // session Dates Swiper and dropdown start ------------------------------------
  const [isDateDrop, setIsDateDrop] = useState(false);
  const [activeTab, setActiveTab] = useState("month");
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);
  const [selectedYear, setSelectedYear] = useState(currentYear);

  // Activities section monthly filter state
  const [isActivityDateDrop, setIsActivityDateDrop] = useState(false);
  const [activityActiveTab, setActivityActiveTab] = useState("month");
  const [activitySelectedMonth, setActivitySelectedMonth] = useState(currentMonth);
  const [activitySelectedYear, setActivitySelectedYear] = useState(currentYear);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const activityDropdownRef = useRef<HTMLDivElement>(null);

  const [dates, setDates] = useState<{ day: string; weekday: string }[]>([]);
  const [finalDate, setFinalDate] = useState<string>(() => {
    const today = new Date();
    // Set to the start of the day (00:00:00) if needed, or keep it as current time
    today.setHours(0, 0, 0, 0); // Optional: Uncomment this line to set to start of the day
    return today.toISOString();
  });
  const [activeDate, setActiveDate] = useState(today);
  const swiperRef = useRef<SwiperType | null>(null); // Swiper instance or null
  const [hasAutoScrolled, setHasAutoScrolled] = useState(false);

  // Define refs with proper typing
  const prevRef = useRef<HTMLDivElement | null>(null);
  const nextRef = useRef<HTMLDivElement | null>(null);
  // const totalPages = 10; // Define total pages for pagination
  const pageSize = 5;

  const { therapistData } = useGetSettingData();
  const { syncEventStatus } = useCheckSyncCalendarStatus();

  // For public calendar tab, don't send status filter, only send fromPublicCalender filter
  const statusValue =
    activeTable.value === "public-calendar" ? "" : activeTable.value;

  const query =
    `pageSize=${pageSize}&pageNumber=${currentPage}&status=${statusValue}` +
    `&searchText=${debouncedSearchText}` +
    `&client=` +
    (finalDate
      ? `&startDate=${
          finalDate ? moment(finalDate).startOf("day").toISOString() : ""
        }&endDate=${
          finalDate ? moment(finalDate).endOf("day").toISOString() : ""
        }`
      : "");
  // get session schedules
  const { sessionData, sessionLoading, sessionCount, url } = useGetSchedules({
    pageSize,
    currentPage,
    activeTable: statusValue,
    debouncedSearchText,
    finalDate,
    filterparams,
  });

  const totalPages = Math.ceil(sessionCount / pageSize);

  // Reset page to 1 when the active tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTable]);

  // Handle public calendar filter when tab is selected
  useEffect(() => {
    if (activeTable.value === "public-calendar") {
      setFilterparams((prev) => ({
        ...prev,
        fromPublicCalender: true,
      }));
    } else {
      // Remove public calendar filter when switching to other tabs
      setFilterparams((prev) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { fromPublicCalender, ...rest } = prev;
        return rest;
      });
    }
  }, [activeTable.value]);

  // get payment activity data
  const { dashboardStatsData } = useGetDashboardStats(
    activitiesStartDate,
    activitiesEndDate
  ) as unknown as {
    dashboardStatsData: {
      receivedPayment: number;
      pendingPayments: number;
      clients: number;
      schedules: number;
    };
  };

  // Update the useGetMostRecentClientRequest hook type
  const {} = useGetMostRecentClientRequest() as {
      mostRecentClientData: MostRecentClientItem[];
      mostRecentClientLoading: boolean;
    };

  const {} = useGetCancellation({
    page: 1,
    pageSize: 4,
    startDate: cancelledStartDate,
    endDate: cancelledEndDate,
  });

  const activity = [
    {
      title: "Received payments",
      count: `${dashboardStatsData?.receivedPayment || 0}`,
      icon: <ReceivedPayIcon className="w-30px h-auto" />,
    },
    {
      title: "Pending payments",
      count: `${dashboardStatsData?.pendingPayments || 0}`,
      icon: <PendingPayIcon className="w-30px h-auto" />,
    },
    {
      title: "Patients",
      count: `${dashboardStatsData?.clients || 0}`,
      icon: <GreenUsersIcon className="w-30px h-auto" />,
    },
    {
      title: "Sessions",
      count: `${dashboardStatsData?.schedules || 0}`,
      icon: <OrangeCalendarIcon className="w-30px h-auto" />,
    },
  ];

  const months = useMemo(() => [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ], []);

  // Generate years dynamically including past and future years
  const currentFullYear = new Date().getFullYear();
  const years = [];
  let pastYear = currentFullYear;
  let futureYear = currentFullYear + 1;

  while (true) {
    years.unshift(pastYear);
    pastYear--;
    if (pastYear < 2000) break;
  }
  while (true) {
    years.push(futureYear);
    futureYear++;
    if (futureYear > currentFullYear + 100) break;
  }

  const deleteSingleSession = async (cancelled_session: string) => {
    try {
      setLoading(true);
      await deleteSchedules(singleSessionID, cancelled_session);

      mutate(url);
      setIsUpdatePaymentModal(false);
      setIsTerminatingModal(false);
    } catch (error) {
      console.error("Failed to delete the session.", error);
    } finally {
      setLoading(false);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDateDrop(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Close Activities dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        activityDropdownRef.current &&
        !activityDropdownRef.current.contains(event.target as Node)
      ) {
        setIsActivityDateDrop(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSelectMonth = (month: string) => {
    setSelectedMonth(month);
    setActiveDate("1");
    setIsDateDrop(false);

    // Update finalDate to the first day of the selected month and year
    const newDate = dateFormatter(selectedYear, month, 1);
    setFinalDate(newDate);

    // Reset Swiper position when changing month
    if (swiperRef.current) {
      swiperRef.current.slideTo(0);
    }
  };

  const handleSelectYear = (year: number) => {
    setSelectedYear(year.toString());
    setActiveDate("1");
    setIsDateDrop(false);

    // Update finalDate to the first day of the selected year and month
    const newDate = dateFormatter(year.toString(), selectedMonth, 1);
    setFinalDate(newDate);

    // Reset Swiper position when changing year
    if (swiperRef.current) {
      swiperRef.current.slideTo(0);
    }
  };

  const handleDateClick = (day: number) => {
    setActiveDate(day.toString()); // Set the clicked date as active
    const fullDate = dateFormatter(selectedYear, selectedMonth, day);
    setFinalDate(fullDate);
  };

  // Activities section handlers
  const handleActivitySelectMonth = (month: string) => {
    setActivitySelectedMonth(month);
    setIsActivityDateDrop(false);

    // Update activities date range when month changes
    const monthIndex = months.indexOf(month);
    const year = parseInt(activitySelectedYear);

    // Set start date to first day of selected month
    const startDate = new Date(year, monthIndex, 1);
    startDate.setHours(0, 0, 0, 0);
    setActivitiesStartDate(startDate);

    // Set end date to last day of selected month
    const endDate = new Date(year, monthIndex + 1, 0);
    endDate.setHours(23, 59, 59, 999);
    setActivitiesEndDate(endDate);
  };

  const handleActivitySelectYear = (year: number) => {
    setActivitySelectedYear(year.toString());
    setIsActivityDateDrop(false);

    // Update activities date range when year changes
    const monthIndex = months.indexOf(activitySelectedMonth);

    // Set start date to first day of selected month and year
    const startDate = new Date(year, monthIndex, 1);
    startDate.setHours(0, 0, 0, 0);
    setActivitiesStartDate(startDate);

    // Set end date to last day of selected month and year
    const endDate = new Date(year, monthIndex + 1, 0);
    endDate.setHours(23, 59, 59, 999);
    setActivitiesEndDate(endDate);
  };

  const generateDates = useCallback(
    (month: string, year: number) => {
      // Generate the dates based on the selected month and year
      const daysInMonth = new Date(
        year,
        months.indexOf(month) + 1,
        0
      ).getDate();
      const dateArray = Array.from({ length: daysInMonth }, (_, i) => ({
        day: String(i + 1).padStart(2, "0"),
        weekday: new Date(year, months.indexOf(month), i + 1).toLocaleString(
          "en-US",
          { weekday: "short" }
        ),
      }));
      setDates(dateArray);
    },
    [months]
  );

  // Find the index of the initial active slide (based on activeDate)
  const initialSlideIndex = dates.findIndex((date) => date.day === activeDate);

  // send reminder of session
  async function reminderSessionData() {
    try {
      setLoading(true); // Start loader
      const response = await scheduleReminder(singleSessionID);
      return response;
    } catch (error) {
      console.error("Failed to schedule reminder.", error);
    } finally {
      setLoading(false); // Stop loader
    }
  }

  // Use effect to slide to the active slide on load (only once)
  useEffect(() => {
    if (!hasAutoScrolled && swiperRef.current && initialSlideIndex !== -1) {
      swiperRef.current.slideTo(initialSlideIndex, 0);
      setHasAutoScrolled(true); // Set flag to prevent further auto-scrolls
    }
  }, [initialSlideIndex, hasAutoScrolled]);

  useEffect(() => {
    generateDates(selectedMonth, Number(selectedYear));
  }, [selectedMonth, selectedYear, generateDates]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchText(searchText);
      setCurrentPage(1);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchText]);

  useEffect(() => {
    if (syncEventStatus) setIsOpenSyncCalender(true);
  }, [syncEventStatus]);

  // Removed duplicate scroll management - Modal component handles this

  useEffect(() => {}, [isScheduleSessionModal]);

  // Add new state for analytics dates
  const [analyticsFromDate, setAnalyticsFromDate] = useState(() => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  });

  const [analyticsToDate, setAnalyticsToDate] = useState(() => {
    const now = new Date();
    // Use today's date instead of end of month to avoid future dates
    return now;
  });

  // Add hooks for all analytics data with null keys initially
  const { totalWorkingHoursData } = useGetTotalWorkingHours(
    analyticsFromDate?.toISOString() || '',
    analyticsToDate?.toISOString() || ''
  );

  const { strongRelationshipsData } = useGetStrongRelationships(
    analyticsFromDate?.toISOString() || '',
    analyticsToDate?.toISOString() || ''
  );

  const { peopleHelpingData } = useGetPeopleHelping(
    analyticsFromDate?.toISOString() || '',
    analyticsToDate?.toISOString() || ''
  );

  const { ruptureRepairData } = useGetRuptureRepair(
    analyticsFromDate?.toISOString() || '',
    analyticsToDate?.toISOString() || ''
  );

  // Add the new hook
  const {} = useGetBusiestTimeSlots(
    analyticsFromDate?.toISOString() || '',
    analyticsToDate?.toISOString() || ''
  );

  // Add separate state for payment overview dates
  const [paymentFromDate, setPaymentFromDate] = useState(() => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  });

  const [paymentToDate, setPaymentToDate] = useState(() => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth() + 1, 0);
  });

  // Add separate state for cancellation overview dates
  const [cancellationOverviewFromDate, setCancellationOverviewFromDate] = useState(() => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  });

  const [cancellationOverviewToDate, setCancellationOverviewToDate] = useState(() => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth() + 1, 0);
  });

  const { paymentSummaryData } = useGetPaymentSummary(
    paymentFromDate?.toISOString() || '',
    paymentToDate?.toISOString() || ''
  );

  // Get cancellation data for the overview chart (separate from the existing cancellation data)
  const { cancellationData: cancellationOverviewData } = useGetCancellation({
    page: 1,
    pageSize: 10, // Get more data for the overview chart
    startDate: cancellationOverviewFromDate,
    endDate: cancellationOverviewToDate,
  });

  // Add new state for business data dates
  const [businessFromDate, setBusinessFromDate] = useState(() => {
    const now = new Date();
    return startOfWeek(now);
  });
  
  const [businessToDate, setBusinessToDate] = useState(() => {
    const now = new Date();
    return endOfWeek(now);
  });

  // Business data hooks
  const {
    busiestTimeSlotsData: businessBusiestTimeSlotsData
  } = useGetBusiestTimeSlots(
    businessFromDate?.toISOString() || '',
    businessToDate?.toISOString() || ''
  );

  const {
    busiestDaysData: businessBusiestDaysData
  } = useGetBusiestDays(
    businessFromDate?.toISOString() || '',
    businessToDate?.toISOString() || ''
  );

  const {
    highestCancellationDayData: businessHighestCancellationDayData
  } = useGetHighestCancellationDay(
    businessFromDate?.toISOString() || '',
    businessToDate?.toISOString() || ''
  );

  // Add business date change handler
  const handleBusinessDateApply = (fromDate: Date, toDate: Date) => {
    setBusinessFromDate(fromDate);
    setBusinessToDate(toDate);
  };

  // Format time slots for display
  const formatTimeSlots = (timeSlots: BusiestTimeSlotsData['busiestTimeSlots'] | undefined) => {
    if (!timeSlots || timeSlots.length === 0) return { value: 'No data', subtitle: '' };

    // Take only top 2 slots if multiple exist
    const topSlots = timeSlots.slice(0, 2);

    // Format each slot from "6:00 AM - 9:00 AM" to "6-9 AM"
    const formattedSlots = topSlots.map((slot: { timeSlot: string }) => {
      const [start, end] = slot.timeSlot.split(' - ');
      const startTime = start.split(':')[0];
      const endTime = end.split(':')[0];
      const period = end.split(' ')[1];
      return `${startTime}-${endTime} ${period}`;
    });

    return {
      value: formattedSlots.join(' & '),
      subtitle: `window ${timeSlots.length === 1 ? 'has' : 'have'} the highest occupancy`
    };
  };

  // Format busiest days for display
  const formatBusiestDays = (busiestDays: BusiestDaysData['busiestDays'] | undefined) => {
    if (!busiestDays || busiestDays.length === 0) return { value: 'No data', subtitle: '' };

    // Take only top 2 days if multiple exist
    const topDays = busiestDays.slice(0, 2);

    const formattedDays = topDays.map(day => day.dayOfWeek);

    return {
      value: formattedDays.join(' & '),
      subtitle: `${topDays.length === 1 ? 'has' : 'have'} the highest session.`
    };
  };

  // Format highest cancellation days for display
  const formatHighestCancellationDays = (cancellationDays: HighestCancellationDayData['highestCancellationDays'] | undefined) => {
    if (!cancellationDays || cancellationDays.length === 0) return { value: 'No data', subtitle: '' };

    // Take only top 2 days if multiple exist
    const topDays = cancellationDays.slice(0, 1);

    const formattedDays = topDays.map(day => day.dayOfWeek);

    return {
      value: formattedDays.join(' & '),
      subtitle: `${topDays.length === 1 ? 'has' : 'have'} the highest cancellations.`
    };
  };

  // Transform payment summary data to payment overview format
  const transformPaymentSummaryData = (data: PaymentSummaryData | undefined) => {
    if (!data) {
      return {
        avgSessionFee: 0,
        noOfSessions: 0,
        totalPayment: 0,
        receivedAfterSessions: { amount: 0, percentage: 0 },
        cancellationFees: { amount: 0, percentage: 0 },
        advancePayment: { amount: 0, percentage: 0 },
      };
    }

    const { paymentSummary } = data;
    const totalAmount = paymentSummary.summary.totalAmount;

    // Calculate percentages
    const receivedPercentage = totalAmount > 0 ? (paymentSummary.receivedAfterSessions.amount / totalAmount) * 100 : 0;
    const cancellationPercentage = totalAmount > 0 ? (paymentSummary.cancellationFees.amount / totalAmount) * 100 : 0;
    const advancePercentage = totalAmount > 0 ? (paymentSummary.advancePayments.amount / totalAmount) * 100 : 0;

    // Calculate average session fee
    const avgSessionFee = paymentSummary.summary.totalTransactions > 0
      ? totalAmount / paymentSummary.summary.totalTransactions
      : 0;

    return {
      avgSessionFee: Math.round(avgSessionFee),
      noOfSessions: paymentSummary.summary.totalTransactions,
      totalPayment: totalAmount,
      receivedAfterSessions: {
        amount: paymentSummary.receivedAfterSessions.amount,
        percentage: Math.round(receivedPercentage),
      },
      cancellationFees: {
        amount: paymentSummary.cancellationFees.amount,
        percentage: Math.round(cancellationPercentage),
      },
      advancePayment: {
        amount: paymentSummary.advancePayments.amount,
        percentage: Math.round(advancePercentage),
      },
    };
  };

  // Transform payment summary API data for the payment overview chart
  const paymentOverviewData = transformPaymentSummaryData(paymentSummaryData);

  // Business data items
  const businessDataItems = [
    {
      title: "Time You're Most in Demand",
      ...formatTimeSlots(businessBusiestTimeSlotsData?.busiestTimeSlots),
      color: "#F2E9FF",
      textColor: "#9974C8",
      imageWidth: 137,
      imageHeight: 137,
    },
    {
      title: "Busiest Day Of The Week",
      ...formatBusiestDays(businessBusiestDaysData?.busiestDays),
      color: "#FBE9D0",
      textColor: "#D8A155",
      imageWidth: 112,
      imageHeight: 112,
    },
    {
      title: "Highest Cancellation Day Of The Week",
      ...formatHighestCancellationDays(businessHighestCancellationDayData?.highestCancellationDays),
      color: "#FBD7D8",
      textColor: "#DD6877",
      imageWidth: 110,
      imageHeight: 110,
    },
  ];

  // Analytics date change handler removed as it's not used

  return (
    <DashboardLayout>
      <div className="space-y-5 pt-5">
        <div className="grid md:grid-cols-3 gap-5">
          <div className="bg-white md:p-5 p-3 rounded-base flex items-center justify-between">
            <div>
              <h2 className="text-base/5 sm:text-lg/6 font-semibold text-primary">
                Hi {therapistData?.name}!
              </h2>
              <p className="text-base/5 text-primary/70 pt-3">
                You have an exciting week ahead of you
              </p>
            </div>
            <div className="w-[68px] h-auto md:block hidden">
              <Image
                src="/assets/images/dashboard/hand.webp"
                alt="logo"
                width={600}
                height={600}
                className="w-full h-full"
              />
            </div>
          </div>
          <div className="bg-white md:p-5 p-3 rounded-base">
            <div>
              <h2 className="text-base/5 sm:text-lg/6 font-semibold text-primary capitalize">
                New Appointment
              </h2>
              <p className="text-sm/5 text-primary/70 pt-3 capitalize">
                Guide users through booking a specific time.
              </p>
            </div>
            <div className="flex flex-wrap md:gap-5 gap-3 items-center mt-5">
              <Button onClick={() => setFreeSlot(!freeSlot)} variant="default">
                Find me free slot
              </Button>
              <Button
                onClick={() =>
                  setIsScheduleSessionModal(!isScheduleSessionModal)
                }
                variant="outlined"
              >
                Schedule a session
              </Button>
            </div>
          </div>
          <div className="bg-white md:p-5 p-3 pr-1 rounded-base">
            <div className="max-w-[422px]">
              <h2 className="text-base/5 sm:text-lg/6 font-semibold text-primary">
                Sync Your Google Calendar
              </h2>
              <p className="text-sm/5 text-primary/70 pt-3">
                Schedule Sessions and timely Reminders With just one click.
              </p>
            </div>

            <Button
              variant="filled"
              className="max-w-[177px] w-full mt-5"
              onClick={() => setIsOpenSyncCalender(true)}
            >
              Setup calendar
            </Button>
          </div>
        </div>

        {/* Activity */}
        <div className="md:p-5 p-3 rounded-base bg-white">
          <div className="md:flex justify-between items-center">
            <h2 className="text-base sm:text-lg/5 md:text-xl/6 font-semibold text-primary">
              Activities
            </h2>
            <div className="relative mt-3 md:mt-0 flex justify-start md:justify-end" ref={activityDropdownRef}>
              <button
                className="relative flex items-center gap-2 text-base/6 font-medium text-gray-500"
                onClick={() => setIsActivityDateDrop(!isActivityDateDrop)}
              >
                <span>{`${activitySelectedMonth}, ${activitySelectedYear}`}</span>
                <CaretDown
                  size={20}
                  className={`transition-all duration-300 ${
                    isActivityDateDrop ? "rotate-180" : ""
                  }`}
                />
              </button>
              <div
                className={`absolute top-full right-0 w-[360px] bg-white rounded-2xl shadow-[0px_4px_12px_0px_#2C58BB1A] overflow-hidden transition-all duration-300 z-20 ${
                  isActivityDateDrop ? "h-[428px]" : "h-0"
                }`}
              >
                <div className="grid grid-cols-2 gap-2.5">
                  <button
                    className={`p-[22px] font-medium text-sm_18 flex items-end justify-center gap-2 ${
                      activityActiveTab === "month"
                        ? "text-primary"
                        : "text-primary/70"
                    }`}
                    onClick={() => setActivityActiveTab("month")}
                  >
                    {activitySelectedMonth}{" "}
                    {activityActiveTab === "month" && (
                      <CaretDown size={18} weight="fill" />
                    )}
                  </button>
                  <button
                    className={`p-[22px] font-medium text-sm_18 flex items-end justify-center gap-2 ${
                      activityActiveTab === "year"
                        ? "text-primary"
                        : "text-primary/70"
                    }`}
                    onClick={() => setActivityActiveTab("year")}
                  >
                    {activitySelectedYear}
                    {activityActiveTab === "year" && (
                      <CaretDown size={18} weight="fill" />
                    )}
                  </button>
                </div>
                <hr className="border-[#CAC4D0]" />
                <div className="pt-2 pb-5 overflow-y-auto h-[calc(100%-50px)]">
                  {activityActiveTab === "month" && (
                    <ul className="month">
                      {months.map((month) => (
                        <li
                          key={month}
                          className={`py-3 px-4 text-sm_18 text-[#1D1B20] cursor-pointer flex items-center gap-4  hover:bg-gray-100/20 ${
                            month === activitySelectedMonth
                              ? "bg-green-600/10 !text-green-600"
                              : ""
                          }`}
                          onClick={() => handleActivitySelectMonth(month)}
                        >
                          <div className="w-6">
                            {month === activitySelectedMonth && <Check size={24} />}
                          </div>
                          {month}
                        </li>
                      ))}
                    </ul>
                  )}
                  {activityActiveTab === "year" && (
                    <ul className="years">
                      {years.map((year) => (
                        <li
                          key={year}
                          className={`py-3 px-4 text-sm_18 text-[#1D1B20] cursor-pointer flex items-center  gap-4  hover:bg-gray-100/20 ${
                            String(year) === activitySelectedYear
                              ? "bg-green-600/10 !text-green-600"
                              : ""
                          }`}
                          onClick={() => handleActivitySelectYear(year)}
                        >
                          <div className="w-6">
                            {String(year) === activitySelectedYear && (
                              <Check size={24} />
                            )}
                          </div>
                          {year}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="grid md:grid-cols-4  gap-5 pt-5">
            {activity?.map((item, index) => (
              <div
                key={index}
                className={`p-5  rounded-base flex justify-between items-center ${
                  item.title.trim() === "Received payments"
                    ? "bg-[#FBE9D0]"
                    : item.title.trim() === "Pending payments"
                    ? "bg-[#D8E5FF]"
                    : item.title.trim() === "Patients"
                    ? "bg-[#FBD7D8]"
                    : item.title.trim() === "Sessions"
                    ? "bg-[#F8DBFF]"
                    : ""
                }`}
              >
                <div>
                  <p className="text-2xl/7 font-medium text-primary">
                    {item.count}
                  </p>
                  <p className="capitalize text-base/5 text-primary  pt-3">
                    {item.title}
                  </p>
                </div>
                <div
                  className={`p-4 rounded-full ${
                    item.title.trim() === "Received payments"
                      ? "bg-[#FFD7A0]"
                      : item.title.trim() === "Pending payments"
                      ? "bg-[#A0BEFF]"
                      : item.title.trim() === "Patients"
                      ? "bg-[#FDC0C1]"
                      : item.title.trim() === "Sessions"
                      ? "bg-[#F1C2FE]"
                      : ""
                  }`}
                >
                  {item.icon}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* session area */}
        <div className=" rounded-base bg-white">
          <div className="md:p-5 p-3">
            <div className="flex items-center justify-between">
              <h2 className="text-lg/5 sm:text-xl/6 font-semibold text-primary">
                Sessions
              </h2>
              <div className="relative" ref={dropdownRef}>
                <button
                  className="relative flex items-center gap-2 text-base/6 font-medium text-gray-500"
                  onClick={() => setIsDateDrop(!isDateDrop)}
                >
                  <span>{`${selectedMonth}, ${selectedYear}`}</span>
                  <CaretDown
                    size={20}
                    className={`transition-all duration-300 ${
                      isDateDrop ? "rotate-180" : ""
                    }`}
                  />
                </button>
                <div
                  className={`absolute top-full right-0 w-[360px] bg-white rounded-2xl shadow-[0px_4px_12px_0px_#2C58BB1A] overflow-hidden transition-all duration-300 z-20 ${
                    isDateDrop ? "h-[428px]" : "h-0"
                  }`}
                >
                  <div className="grid grid-cols-2 gap-2.5">
                    <button
                      className={`p-[22px] font-medium text-sm_18 flex items-end justify-center gap-2 ${
                        activeTab === "month"
                          ? "text-primary"
                          : "text-primary/70"
                      }`}
                      onClick={() => setActiveTab("month")}
                    >
                      {selectedMonth}{" "}
                      {activeTab === "month" && (
                        <CaretDown size={18} weight="fill" />
                      )}
                    </button>
                    <button
                      className={`p-[22px] font-medium text-sm_18 flex items-end justify-center gap-2 ${
                        activeTab === "year"
                          ? "text-primary"
                          : "text-primary/70"
                      }`}
                      onClick={() => setActiveTab("year")}
                    >
                      {selectedYear}
                      {activeTab === "year" && (
                        <CaretDown size={18} weight="fill" />
                      )}
                    </button>
                  </div>
                  <hr className="border-[#CAC4D0]" />
                  <div className="pt-2 pb-5 overflow-y-auto h-[calc(100%-50px)]">
                    {activeTab === "month" && (
                      <ul className="month">
                        {months.map((month) => (
                          <li
                            key={month}
                            className={`py-3 px-4 text-sm_18 text-[#1D1B20] cursor-pointer flex items-center gap-4  hover:bg-gray-100/20 ${
                              month === selectedMonth
                                ? "bg-green-600/10 !text-green-600"
                                : ""
                            }`}
                            onClick={() => handleSelectMonth(month)}
                          >
                            <div className="w-6">
                              {month === selectedMonth && <Check size={24} />}
                            </div>
                            {month}
                          </li>
                        ))}
                      </ul>
                    )}
                    {activeTab === "year" && (
                      <ul className="years">
                        {years.map((year) => (
                          <li
                            key={year}
                            className={`py-3 px-4 text-sm_18 text-[#1D1B20] cursor-pointer flex items-center  gap-4  hover:bg-gray-100/20 ${
                              String(year) === selectedYear
                                ? "bg-green-600/10 !text-green-600"
                                : ""
                            }`}
                            onClick={() => handleSelectYear(year)}
                          >
                            <div className="w-6">
                              {String(year) === selectedYear && (
                                <Check size={24} />
                              )}
                            </div>
                            {year}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-5 relative">
              <div className="max-w-[1152px] mx-auto ">
                <Swiper
                  slidesPerView={9}
                  spaceBetween={30}
                  modules={[Navigation]}
                  className="DateSwiper"
                  navigation={{
                    prevEl: prevRef.current,
                    nextEl: nextRef.current,
                  }}
                  breakpoints={{
                    320: { slidesPerView: 4 }, // For screens >= 320px (mobile)
                    768: { slidesPerView: 6 }, // For screens >= 768px (tablet)
                    1024: { slidesPerView: 9 }, // For screens >= 1024px (desktop)
                  }}
                  onSwiper={(swiper) => {
                    swiperRef.current = swiper; // Capture the swiper instance
                  }}
                  initialSlide={
                    initialSlideIndex !== -1 ? initialSlideIndex : 0
                  } // Set the initial slide to the activeDate
                >
                  {dates.map((date) => (
                    <SwiperSlide key={date.day}>
                      <div className=" flex items-center justify-center text-center">
                        <button
                          className={` group transition-all duration-500 relative z-30 ${
                            Number(activeDate) === Number(date.day)
                              ? "text-yellow-600 font-semibold "
                              : "text-primary"
                          }`}
                          onClick={() => handleDateClick(Number(date.day))}
                        >
                          <p className="sm:text-[26px] sm:leading-[30px] text-xl group-hover:font-semibold group-focus:font-semibold">
                            {date.day}
                          </p>
                          <p className="text-sm leading-[30px]">
                            {date.weekday}
                          </p>
                        </button>
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                <button
                  ref={prevRef as React.RefObject<HTMLButtonElement>}
                  className="absolute top-1/2 left-2 transform -translate-y-1/2 rounded-full z-10 md:block hidden"
                >
                  <svg
                    width="20"
                    height="21"
                    viewBox="0 0 20 21"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect
                      width="20"
                      height="20"
                      rx="10"
                      transform="matrix(1 -8.74228e-08 -8.74228e-08 -1 0 20.5)"
                      fill="#F5F5F7"
                    />
                    <path
                      d="M7.21056 11.0074L11.4901 15.2901C11.6244 15.4245 11.8065 15.5 11.9965 15.5C12.1864 15.5 12.3686 15.4245 12.5029 15.2901C12.6372 15.1557 12.7126 14.9734 12.7126 14.7833C12.7126 14.5932 12.6372 14.4109 12.5029 14.2765L8.72265 10.5006L12.5029 6.7247C12.5697 6.65835 12.6228 6.5794 12.659 6.49242C12.6952 6.40544 12.7139 6.31215 12.7139 6.21792C12.7139 6.12369 12.6952 6.0304 12.659 5.94342C12.6228 5.85644 12.5697 5.77749 12.5029 5.71114C12.4366 5.64423 12.3577 5.59113 12.2708 5.55489C12.1839 5.51866 12.0906 5.5 11.9965 5.5C11.9023 5.5 11.8091 5.51866 11.7222 5.55489C11.6353 5.59113 11.5564 5.64423 11.4901 5.71114L7.21056 9.99378C7.07568 10.1282 7 10.3104 7 10.5004C7 10.6904 7.07568 10.8726 7.21056 11.0074Z"
                      fill="#5E5F6E"
                    />
                  </svg>
                </button>
                <button
                  type="button"
                  ref={nextRef as React.RefObject<HTMLButtonElement>}
                  className="absolute top-1/2 right-2 transform -translate-y-1/2 rounded-full z-10 md:block hidden"
                >
                  <svg
                    width="20"
                    height="21"
                    viewBox="0 0 20 21"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect
                      width="20"
                      height="20"
                      rx="10"
                      transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 20 0.5)"
                      fill="#F5F5F7"
                    />
                    <path
                      d="M12.7894 9.99257L8.50991 5.70994C8.37563 5.57553 8.19346 5.5 8.00348 5.5C7.81351 5.5 7.63134 5.57553 7.49706 5.70994C7.36278 5.84435 7.28741 6.02658 7.28741 6.21665C7.28741 6.40672 7.36278 6.58895 7.49706 6.72336L11.2773 10.4993L7.49706 14.2752C7.43032 14.3416 7.37717 14.4206 7.34101 14.5076C7.30485 14.5946 7.28613 14.6879 7.28613 14.7821C7.28613 14.8763 7.30485 14.9696 7.34101 15.0566C7.37717 15.1436 7.43032 15.2226 7.49706 15.289C7.56342 15.3558 7.64236 15.4089 7.72934 15.4451C7.81632 15.4813 7.90961 15.5 8.00348 15.5C8.09736 15.5 8.19065 15.4813 8.27763 15.4451C8.36461 15.4089 8.44355 15.3558 8.50991 15.289L12.7894 11.0063C12.9243 10.872 13 10.6896 13 10.4996C13 10.3096 12.9243 10.1274 12.7894 9.99257Z"
                      fill="#5E5F6E"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <hr className="border-gray-100 my-15px " />

            <div className="flex flex-wrap gap-5 items-center justify-between">
              <Tabs
                tabs={sessionTabs}
                activeTab={activeTable.label}
                setActiveTab={(tab) => setActiveTable(tab)}
                sessionCount={sessionCount}
              />

              <div className="flex items-center gap-2 max-w-[351px] w-full sm:py-15px sm:px-5 p-3 border border-[#9B9DB7] rounded-full text-xs text-primary">
                <MagnifyingGlass size={20} className="text-primary/50" />
                <input
                  type="search"
                  placeholder="Search session"
                  className="outline-none w-full placeholder:text-primary/50"
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </div>
            </div>

            {/* session table */}
            <div className="pt-30px">
              <div className="w-full border border-green-600/25 rounded-base overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full  bg-white">
                    <THeader data={sessionTableHeader} />
                    <DashboardTBody
                      TableData={sessionData}
                      sessionLoading={sessionLoading}
                      setIsRescheduleSession={setIsRescheduleSession}
                      setIsReminderModal={setIsReminderMassageModal}
                      setIsCanceledSessionModal={setIsCanceledSessionModal}
                      isCanceledSessionModal={isCanceledSessionModal}
                      setSingleSessionID={setSingleSessionID}
                      setSingleSessionData={(item: Item) =>
                        setSingleSessionData(item as SessionData)
                      }
                      therapistData={therapistData}
                    />
                  </table>
                </div>
                <TablePagination
                  totalPages={totalPages}
                  currentPage={currentPage}
                  onPageChange={setCurrentPage}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Main Card */}
        <div
          className="bg-white p-3 sm:p-5 rounded-base font-poppins"
        >
          <div className="flex flex-col sm:flex-row sm:items-center justify-between pb-4 sm:pb-5">
            <h3 className="text-base sm:text-lg font-semibold text-primary mb-2 sm:mb-0 font-poppins">
              Your Practice This Month ❤️
            </h3>
            <div className="flex justify-start md:justify-end">
              <AnalyticsDateFilter
                onApply={(fromDate, toDate) => {
                  setAnalyticsFromDate(fromDate);
                  setAnalyticsToDate(toDate);
                }}
              />
            </div>
          </div>

          {/* Check if all analytics values are 0 */}
          {(() => {
            const sessionCount = totalWorkingHoursData?.sessionCount || 0;
            const powerClientsCount = strongRelationshipsData?.powerClientsCount || 0;
            const activeClientsCount = peopleHelpingData?.totalActiveClients || 0;
            const riskClientsCount = ruptureRepairData?.totalRiskClients || 0;

            const allValuesZero = sessionCount === 0 && powerClientsCount === 0 && activeClientsCount === 0 && riskClientsCount === 0;

            if (allValuesZero) {
              return (
                <div className="flex items-center justify-center py-16 px-4">
                  <p className="text-center text-gray-600 text-base md:text-lg font-medium max-w-md">
                    Once your sessions are synced, this space will start reflecting the patterns in your practice
                  </p>
                </div>
              );
            }

            return (
              <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-5">
                <AnalyticsCard
                  color="#F2E9FF"
                  imagePath="/assets/images/analytics/frame1.png"
                  title="Total Therapy Hours"
                  value={totalWorkingHoursData?.workingHours || '0h 0m'}
                  textColor="#9974C8"
                  subtitle={`${totalWorkingHoursData?.sessionCount || 0} Sessions`}
                />
                <AnalyticsCard
                  color="#FBE9D0"
                  imagePath="/assets/images/analytics/frame2.png"
                  title="Strong Relationships With"
                  value={strongRelationshipsData?.powerClientsCount || 0}
                  textColor="#D8A155"
                  showClients={true}
                />
                <AnalyticsCard
                  color="#D8E5FF"
                  imagePath="/assets/images/analytics/frame3.png"
                  title="People You're Helping"
                  value={peopleHelpingData?.totalActiveClients || 0}
                  textColor="#5B84D5"
                  showClients={true}
                />
                <AnalyticsCard
                  color="#FBD7D8"
                  imagePath="/assets/images/analytics/frame4.png"
                  title="Rupture & Repair Needed With"
                  value={ruptureRepairData?.totalRiskClients || 0}
                  textColor="#DD6877"
                  showClients={true}
                />
              </div>
            );
          })()}
        </div>

        {/* Analytics Sections */}
        <div className="mt-8">
          {/* Extra Large screens (1400px+): All 3 sections in one row */}
          <div className="hidden 2xl:flex 2xl:flex-row gap-6">
            {/* Left Column - Business Data Overview */}
            <div className="2xl:w-[424px] bg-white p-3 sm:p-5 rounded-base font-poppins">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-5">
                <h3 className="text-base sm:text-lg font-semibold text-primary mb-2 sm:mb-0 font-poppins">
                  Business Data Overview
                </h3>
                <div className="flex justify-start md:justify-end">
                  <BusinessDateFilter onApply={handleBusinessDateApply} />
                </div>
              </div>
              <BusinessDataOverview data={businessDataItems} />
            </div>

            {/* Center Column - Payment/Cancellation Overview Chart */}
            <div className="2xl:w-[358px] flex justify-center">
              <PaymentOverviewChart
                data={paymentOverviewData}
                cancellationData={cancellationOverviewData}
                onPaymentDateChange={(startDate: Date, endDate: Date) => {
                  setPaymentFromDate(startDate);
                  setPaymentToDate(endDate);
                }}
                onCancellationDateChange={(startDate: Date, endDate: Date) => {
                  setCancellationOverviewFromDate(startDate);
                  setCancellationOverviewToDate(endDate);
                }}
              />
            </div>

            {/* Right Column - Client Insights Accordion */}
            <div className="2xl:w-[563px]">
              <ClientInsightsAccordion
                sections={accordionSections}
                powerClientsData={strongRelationshipsData?.powerClientsData}
                riskClientsData={ruptureRepairData?.riskClients}
                activeClientsData={peopleHelpingData?.activeClients}
                fromDate={analyticsFromDate}
                toDate={analyticsToDate}
              />
            </div>
          </div>

          {/* Large, Medium and Small screens: Responsive layout */}
          <div className="2xl:hidden space-y-6">
            {/* First Row - Business Data Overview + Payment Overview Chart (medium screens only) */}
            <div className="flex flex-col md:flex-row gap-6">
              {/* Left Column - Business Data Overview */}
              <div className="w-full md:flex-1 md:min-w-0 bg-white md:p-5 p-3 rounded-base font-poppins">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-5">
                  <h3 className="text-base sm:text-lg font-semibold text-primary mb-2 sm:mb-0 font-poppins">
                    Business Data Overview
                  </h3>
                  <div className="flex justify-start md:justify-end">
                    <BusinessDateFilter onApply={handleBusinessDateApply} />
                  </div>
                </div>
                <BusinessDataOverview data={businessDataItems} />
              </div>

              {/* Center Column - Payment/Cancellation Overview Chart */}
              <div className="w-full md:w-auto md:flex-shrink-0">
                <PaymentOverviewChart
                  data={paymentOverviewData}
                  cancellationData={cancellationOverviewData}
                  onPaymentDateChange={(startDate: Date, endDate: Date) => {
                    setPaymentFromDate(startDate);
                    setPaymentToDate(endDate);
                  }}
                  onCancellationDateChange={(startDate: Date, endDate: Date) => {
                    setCancellationOverviewFromDate(startDate);
                    setCancellationOverviewToDate(endDate);
                  }}
                />
              </div>
            </div>

            {/* Second Row - Client Insights Accordion (full width) */}
            <div className="w-full">
              <ClientInsightsAccordion
                sections={accordionSections}
                powerClientsData={strongRelationshipsData?.powerClientsData}
                riskClientsData={ruptureRepairData?.riskClients}
                activeClientsData={peopleHelpingData?.activeClients}
                fromDate={analyticsFromDate}
                toDate={analyticsToDate}
              />
            </div>
          </div>
        </div>
      </div>

      {/* FreeSlots Sidebar */}
      <FreeSlotsSidebar freeSlot={freeSlot} setFreeSlot={setFreeSlot} />

      {/* Schedule Session Sidebar */}
      <ScheduleSessionSidebar
        isScheduleSessionModal={isScheduleSessionModal}
        setIsScheduleSessionModal={setIsScheduleSessionModal}
      />

      {/* Reschedule Session sidebar */}
      <RescheduleSidebar
        isRescheduleSession={isRescheduleSession}
        setIsRescheduleSession={setIsRescheduleSession}
        singleSessionID={singleSessionID}
        singleSessionData={singleSessionData}
        query={query}
      />

      {/* ====== Session modals ====== */}
      {/* Reminder Modal */}
      {/* <CommonModal
        title="Reminder"
        isClose={isReminderModal}
        setIsClose={setIsReminderModal}
      >
        <div className="pt-30px space-y-2.5">
          <label className="flex justify-between items-center text-sm/5 text-gray-500">
            Session Reminder
            <input type="radio" name="reminder" className="w-4.5 h-4.5" />
          </label>
          <label className="flex justify-between items-center text-sm/5 text-gray-500">
            Payment Session
            <input type="radio" name="reminder" className="w-4.5 h-4.5" />
          </label>
          <label className="flex justify-between items-center text-sm/5 text-gray-500">
            Set Both
            <input type="radio" name="reminder" className="w-4.5 h-4.5" />
          </label>
        </div>
        <div className="flex items-center justify-end gap-3.5 pt-[34px]">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsReminderModal(false);
            }}
          >
            No
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            onClick={() =>
              handleModalTransition(
                setIsReminderModal,
                setIsReminderMassageModal
              )
            }
          >
            Yes
          </Button>
        </div>
      </CommonModal> */}

      {/* Reminder massage Modal */}
      <CommonModal
        title="Send reminder"
        isClose={isReminderMassageModal}
        setIsClose={(isClose) => {
          if (!loading) {
            setIsReminderMassageModal(isClose); // Prevent manual close while loading
          }
        }}
        className={`!max-w-[450px]`}
      >
        <div className="text-base/7 text-primary pt-5">
          <p>
            To:{" "}
            <span className="text-green-600 capitalize">
              {singleSessionData?.clientId?.name || "-"}
            </span>{" "}
            <span className="text-green-600 lowercase">
              {`<${singleSessionData?.email || "-"}>`}
            </span>
          </p>
          <p>
            Subject: <span className="font-semibold">Session Reminder 🔔</span>
          </p>
          <p className="text-green-600 font-semibold capitalize">
            Hi {singleSessionData?.clientId?.name || "-"},
          </p>

          <p className="pt-5">
            Your appointment with{" "}
            <span className="font-semibold">{therapistData?.name}</span> is
            scheduled for{" "}
            <span className="font-semibold">
              {singleSessionData &&
                formatDate(
                  moment(singleSessionData?.fromDate).format("YYYY-MM-DD")
                )}{" "}
              at{" "}
              {singleSessionData &&
                moment(singleSessionData?.fromDate).format("HH:mm")}
            </span>
          </p>
          {singleSessionData?.meetLink && (
            <p className="pt-3">
              You can use this link to join the call:{" "}
              <Link
                href={`${singleSessionData?.meetLink}`}
                className="underline text-green-600 font-medium"
              >
                {singleSessionData?.meetLink}
              </Link>
            </p>
          )}
        </div>
        <Button
          variant="filledGreen"
          className={`w-full mt-7.5`}
          onClick={() => {
            if (!loading) {
              reminderSessionData().then(() => {
                setIsReminderMassageModal(false); // Close modal after sending reminder
              });
            }
          }}
          disabled={loading} // Disable button during loading
        >
          {loading ? "Sending..." : "Send Reminder"} {/* Update button text */}
        </Button>
      </CommonModal>

      {/* Session Canceled */}
      <CommonModal
        title="Session Canceled"
        isClose={isCanceledSessionModal}
        setIsClose={setIsCanceledSessionModal}
      >
        <p className="text-gray-500 text-sm/5 pt-5 max-w-[465px]">
          Are you sure you want to mark this session as a Cancel ? This action
          will notify the customer and update your records accordingly.
        </p>
        <div className="flex items-center justify-end gap-3.5 pt-[34px]">
          <Button
            variant="outlinedGreen"
            onClick={() =>
              handleModalTransition(
                setIsCanceledSessionModal,
                setIsTerminatingModal
              )
            }
            className={`min-w-[157px]`}
          >
            cancel entire slot
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            onClick={() =>
              handleModalTransition(
                setIsCanceledSessionModal,
                setIsCancellationModal
              )
            }
          >
            this session only
          </Button>
        </div>
      </CommonModal>

      {/* Cancellation Fees */}
      <CommonModal
        title="What do you want to do?"
        isClose={isCancellationModal}
        setIsClose={setIsCancellationModal}
      >
        <div className="flex items-center justify-end gap-3.5 pt-[34px]">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() =>
              handleModalTransition(
                setIsCancellationModal,
                setIsUpdatePaymentModal
              )
            }
          >
            Free Cancellation
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px]`}
            onClick={() =>
              handleModalTransition(
                setIsCancellationModal,
                setIsUpdatePaymentModal
              )
            }
          >
            Paid Cancellation
          </Button>
        </div>
      </CommonModal>

      {/* Update Payment Session */}
      <CommonModal
        title="Update Payment Session"
        isClose={isUpdatePaymentModal}
        setIsClose={(isClose) => {
          if (!loading) {
            setIsUpdatePaymentModal(isClose); // Only allow closing if not loading
          }
        }}
      >
        <p className="text-gray-500 text-sm/5 pt-5 max-w-[465px]">
          ⚠️ We are calling off this session. after you have got it.
          cancellation sum Remember to update the same information in your
          payment.
        </p>
        <div className="text-end pt-[34px]">
          <Button
            variant="filledGreen"
            className={`min-w-[157px] ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={() => deleteSingleSession("this session only")}
            disabled={loading} // Disable button when loading
          >
            {loading ? "Processing..." : "Okay, got it"}
          </Button>
        </div>
      </CommonModal>

      {/* Are you terminating the client */}
      <CommonModal
        title="Are you terminating the client"
        isClose={isTerminatingModal}
        setIsClose={(isClose) => {
          if (!loading) {
            setIsTerminatingModal(isClose);
          }
        }}
      >
        <p className="text-gray-500 text-sm/5  pt-5 max-w-[465px]">
          This action will end all services with the client. Please confirm to
          proceed.
        </p>
        <div className="flex items-center justify-end gap-3.5 pt-[34px]">
          <Button
            variant="outlinedGreen"
            className={`min-w-[157px]`}
            onClick={() => {
              setIsTerminatingModal(false);
              setIsRescheduleSession(true);
            }}
          >
            No
          </Button>
          <Button
            variant="filledGreen"
            className={`min-w-[157px] ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={() => deleteSingleSession("cancel entire slot")}
            disabled={loading}
          >
            {loading ? "Processing..." : "Yes"}
          </Button>
        </div>
      </CommonModal>
      <Modal
        open={isOpenSetupCalender}
        handler={() => setIsOpenSyncCalender(false)}
      >
        <div className="flex flex-col justify-center gap-y-3">
          <h3 className="font-semibold">
            Get started by syncing your sessions
          </h3>
          <div className="flex justify-center items-center">
            <Image
              src={"/assets/images/google-sync3.webp"}
              alt="warning-icon"
              width={720}
              height={720}
              className="w-36 h-full object-cover"
            />
          </div>
          <div className="flex justify-center items-center px-5">
            <p className="text-gray-500 text-sm">
              You have new sessions in your calendar, kindly sync them
            </p>
          </div>
          <div className="flex justify-end items-center gap-x-4 mt-5">
            <Button
              variant="outlined"
              onClick={() => setIsOpenSyncCalender(false)}
            >
              Cancel
            </Button>
            <Button
              variant="filled"
              className="min-w-24"
              onClick={() => {
                setIsOpenSyncCalender(false);
                router.push("/setting/calendar");
              }}
            >
              Yes
            </Button>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  );
};

export default Dashboard;
